<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {

    if (Auth::check()) {
        return redirect()->route('dashboard');
    }

    return redirect()->route('login');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('/cust', [App\Http\Controllers\STCustomerController::class, 'index']);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/reports.php';
require __DIR__.'/report-example.php';

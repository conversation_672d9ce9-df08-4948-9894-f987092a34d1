export interface ReportBatch {
  id: number;
  institution_code: string;
  branch_code: string;
  report_code: string;
  report_month: number;
  report_year: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  record_count: number;
  validation_results?: ValidationResults;
  error_message?: string;
  processed_at?: string;
  created_at: string;
  updated_at: string;
  
  // Computed attributes
  period_name: string;
  status_label: string;
  completion_percentage: number;
}

export interface ValidationResults {
  valid: boolean;
  warnings: string[];
  errors: string[];
  previous_period_available: boolean;
  comparison_period_available: boolean;
}

export interface ReportBatchFilters {
  year?: number;
  month?: number;
  report_code?: string;
  status?: string;
  institution_code?: string;
  branch_code?: string;
  search?: string;
  per_page?: number;
}

export interface ReportBatchFilterOptions {
  reportCodes: string[];
  years: number[];
  months: number[];
  statuses: string[];
}

export interface DashboardStatistics {
  total_batches: number;
  completed_batches: number;
  pending_batches: number;
  failed_batches: number;
  completion_rate: number;
  monthly_progress: MonthlyProgress[];
}

export interface MonthlyProgress {
  month: number;
  month_name: string;
  total: number;
  completed: number;
}

export interface PeriodSuggestion {
  year: number;
  month: number;
  period_name: string;
}

export interface ExportMetadata {
  report_batch_id?: number;
  report_code?: string;
  period?: string;
  record_count: number;
  exported_at: string;
  total_batches?: number;
  total_records?: number;
}

export interface BulkExportRequest {
  report_batch_ids: number[];
  format: 'json' | 'csv';
}

export interface ReportBatchFormData {
  institution_code: string;
  branch_code: string;
  report_code: string;
  report_month: number;
  report_year: number;
}

export interface ReportBatchUpdateData {
  status?: string;
  record_count?: number;
  validation_results?: ValidationResults;
  error_message?: string;
}

// Status constants
export const REPORT_BATCH_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

// Status colors for UI
export const STATUS_COLORS = {
  [REPORT_BATCH_STATUS.PENDING]: 'yellow',
  [REPORT_BATCH_STATUS.PROCESSING]: 'blue',
  [REPORT_BATCH_STATUS.COMPLETED]: 'green',
  [REPORT_BATCH_STATUS.FAILED]: 'red',
} as const;

// Month names
export const MONTH_NAMES = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
] as const;

import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { 
  ArrowLeft, 
  Download, 
  Edit, 
  RefreshCw,
  Calendar,
  FileText,
  Database,
  CheckCircle,
  AlertTriangle,
  XCircle,
  BarChart3
} from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { toast } from 'sonner';

import { 
  ReportBatch, 
  ValidationResults,
  STATUS_COLORS 
} from '@/types/report-batches';
import { PaginatedData } from '@/types';

interface Props {
  reportBatch: ReportBatch;
  reportData: PaginatedData<any>;
  validationResults: ValidationResults;
  previousPeriod: { year: number; month: number };
  comparisonPeriod: { year: number; month: number };
}

export default function ShowReportBatch({ 
  reportBatch, 
  reportData, 
  validationResults,
  previousPeriod,
  comparisonPeriod 
}: Props) {
  const [activeTab, setActiveTab] = useState('overview');

  const handleExport = (format: 'json' | 'csv') => {
    const url = format === 'json' 
      ? route('report-batches.export.json', reportBatch.id)
      : route('report-batches.export.csv', reportBatch.id);

    window.open(url, '_blank');
    toast.success(`Export (${format.toUpperCase()}) initiated.`);
  };

  const handleRegenerateData = () => {
    if (confirm('Are you sure you want to regenerate the data? This will overwrite existing data.')) {
      router.post(route('report-batches.generate-data', reportBatch.id), {}, {
        onSuccess: () => {
          toast.success('Data regeneration initiated.');
        },
        onError: () => {
          toast.error('Failed to initiate data regeneration.');
        },
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const color = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'gray';
    return (
      <Badge variant={color === 'green' ? 'default' : 'secondary'} className={`bg-${color}-100 text-${color}-800`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const renderValidationStatus = () => {
    return (
      <div className="space-y-4">
        {/* Overall Status */}
        <Alert className={validationResults.valid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {validationResults.valid ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <XCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={validationResults.valid ? 'text-green-800' : 'text-red-800'}>
            {validationResults.valid 
              ? 'All validation checks passed.' 
              : 'Some validation checks failed.'
            }
          </AlertDescription>
        </Alert>

        {/* Validation Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Previous Period Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {previousPeriod.year}-{previousPeriod.month.toString().padStart(2, '0')}
                </span>
                <Badge variant={validationResults.previous_period_available ? 'default' : 'secondary'}>
                  {validationResults.previous_period_available ? 'Available' : 'Not Available'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Comparison Period Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {comparisonPeriod.year}-{comparisonPeriod.month.toString().padStart(2, '0')}
                </span>
                <Badge variant={validationResults.comparison_period_available ? 'default' : 'destructive'}>
                  {validationResults.comparison_period_available ? 'Available' : 'Missing'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Warnings and Errors */}
        {validationResults.warnings.length > 0 && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              <div className="font-medium mb-2">Warnings:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationResults.warnings.map((warning, index) => (
                  <li key={index} className="text-sm">{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {validationResults.errors.length > 0 && (
          <Alert className="border-red-200 bg-red-50">
            <XCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <div className="font-medium mb-2">Errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationResults.errors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  const renderReportData = () => {
    if (reportData.data.length === 0) {
      return (
        <div className="text-center py-8">
          <Database className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Data Available</h3>
          <p className="text-muted-foreground mb-4">
            No report data has been generated for this batch yet.
          </p>
          {reportBatch.status === 'pending' && (
            <Button onClick={handleRegenerateData}>
              Generate Data
            </Button>
          )}
        </div>
      );
    }

    const firstRow = reportData.data[0];
    const columns = Object.keys(firstRow);

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <p className="text-sm text-muted-foreground">
            Showing {reportData.data.length} of {reportData.total} records
          </p>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('json')}>
              <Download className="h-4 w-4 mr-2" />
              Export JSON
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleExport('csv')}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>

        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.slice(0, 8).map((column) => (
                  <TableHead key={column} className="text-xs">
                    {column.replace(/_/g, ' ').toUpperCase()}
                  </TableHead>
                ))}
                {columns.length > 8 && <TableHead className="text-xs">...</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.data.map((row, index) => (
                <TableRow key={index}>
                  {columns.slice(0, 8).map((column) => (
                    <TableCell key={column} className="text-xs">
                      {row[column]?.toString() || '-'}
                    </TableCell>
                  ))}
                  {columns.length > 8 && (
                    <TableCell className="text-xs text-muted-foreground">
                      +{columns.length - 8} more
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };

  return (
    <AppLayout>
      <Head title={`Report Batch - ${reportBatch.period_name}`} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href={route('report-batches.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Report Batches
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {reportBatch.report_code} - {reportBatch.period_name}
              </h1>
              <p className="text-muted-foreground">
                Report batch details and data
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={route('report-batches.edit', reportBatch.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            {reportBatch.status === 'completed' && (
              <>
                <Button variant="outline" onClick={() => handleExport('json')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export JSON
                </Button>
                <Button variant="outline" onClick={() => handleExport('csv')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-1">
                {getStatusBadge(reportBatch.status)}
              </div>
              <p className="text-xs text-muted-foreground">
                {reportBatch.processed_at 
                  ? `Processed ${new Date(reportBatch.processed_at).toLocaleDateString()}`
                  : 'Not processed yet'
                }
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Record Count</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportBatch.record_count.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Total records generated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportBatch.completion_percentage}%</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${reportBatch.completion_percentage}%` }}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Created</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Date(reportBatch.created_at).toLocaleDateString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {new Date(reportBatch.created_at).toLocaleTimeString()}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
            <TabsTrigger value="data">Report Data</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Report Batch Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Institution Code</label>
                      <p className="text-sm">{reportBatch.institution_code}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Branch Code</label>
                      <p className="text-sm">{reportBatch.branch_code}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Report Code</label>
                      <p className="text-sm">{reportBatch.report_code}</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Report Period</label>
                      <p className="text-sm">{reportBatch.period_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <p className="text-sm">{getStatusBadge(reportBatch.status)}</p>
                    </div>
                    {reportBatch.error_message && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Error Message</label>
                        <p className="text-sm text-red-600">{reportBatch.error_message}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="validation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Data Validation Results</CardTitle>
                <CardDescription>
                  Validation status for data availability and requirements
                </CardDescription>
              </CardHeader>
              <CardContent>
                {renderValidationStatus()}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="data" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Report Data</CardTitle>
                <CardDescription>
                  Generated report data for this batch
                </CardDescription>
              </CardHeader>
              <CardContent>
                {renderReportData()}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}

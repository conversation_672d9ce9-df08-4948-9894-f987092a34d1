<?php

use App\Http\Controllers\V2\ReportController;
use App\Http\Controllers\V2\ReportTypeController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Hybrid Report Type routes
    Route::prefix('report-types')->group(function () {
        Route::get('/', [ReportTypeController::class, 'index'])->name('report-types.index');
        Route::get('/create', [ReportTypeController::class, 'create'])->name('report-types.create');
        Route::post('/', [ReportTypeController::class, 'store'])->name('report-types.store');
        Route::get('/{reportType}', [ReportTypeController::class, 'show'])->name('report-types.show');
        Route::get('/{reportType}/edit', [ReportTypeController::class, 'edit'])->name('report-types.edit');
        Route::put('/{reportType}', [ReportTypeController::class, 'update'])->name('report-types.update');
        Route::delete('/{reportType}', [ReportTypeController::class, 'destroy'])->name('report-types.destroy');
    });

    // Hybrid Report routes
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('reports.index');
        Route::get('/create', [ReportController::class, 'create'])->name('reports.create');
        Route::post('/', [ReportController::class, 'store'])->name('reports.store');
        Route::get('/{report}', [ReportController::class, 'show'])->name('reports.show');
        Route::post('/{report}/process', [ReportController::class, 'process'])->name('reports.process');
        Route::get('/{report}/export/json', [ReportController::class, 'exportJson'])->name('reports.export.json');
        Route::get('/{report}/export/csv', [ReportController::class, 'exportCsv'])->name('reports.export.csv');
    });
});

import { Head, Link, useForm, router } from '@inertiajs/react';
import { <PERSON><PERSON><PERSON>t, Save, Loader2 } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

import { ReportBatch, ReportBatchUpdateData } from '@/types/report-batches';

interface Props {
  reportBatch: ReportBatch;
  statuses: string[];
}

export default function EditReportBatch({ reportBatch, statuses }: Props) {
  const { data, setData, put, processing, errors } = useForm<ReportBatchUpdateData>({
    status: reportBatch.status,
    error_message: reportBatch.error_message || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    put(route('report-batches.update', reportBatch.id), {
      onSuccess: () => {
        toast.success('Report batch updated successfully.');
        router.visit(route('report-batches.show', reportBatch.id));
      },
      onError: () => {
        toast.error('Failed to update report batch.');
      },
    });
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Report batch is waiting to be processed';
      case 'processing':
        return 'Report batch is currently being processed';
      case 'completed':
        return 'Report batch has been successfully completed';
      case 'failed':
        return 'Report batch processing has failed';
      default:
        return '';
    }
  };

  return (
    <AppLayout>
      <Head title={`Edit Report Batch - ${reportBatch.period_name}`} />

      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" asChild>
            <Link href={route('report-batches.show', reportBatch.id)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Report Batch
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Report Batch</h1>
            <p className="text-muted-foreground">
              {reportBatch.report_code} - {reportBatch.period_name}
            </p>
          </div>
        </div>

        {/* Report Batch Information */}
        <Card>
          <CardHeader>
            <CardTitle>Report Batch Information</CardTitle>
            <CardDescription>
              Basic information about this report batch (read-only)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Institution Code</Label>
                  <p className="text-sm mt-1">{reportBatch.institution_code}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Branch Code</Label>
                  <p className="text-sm mt-1">{reportBatch.branch_code}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Report Code</Label>
                  <p className="text-sm mt-1">{reportBatch.report_code}</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Report Period</Label>
                  <p className="text-sm mt-1">{reportBatch.period_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Record Count</Label>
                  <p className="text-sm mt-1">{reportBatch.record_count.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Created At</Label>
                  <p className="text-sm mt-1">
                    {new Date(reportBatch.created_at).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Edit Form */}
        <Card>
          <CardHeader>
            <CardTitle>Edit Report Batch</CardTitle>
            <CardDescription>
              Update the status and error information for this report batch
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={data.status}
                  onValueChange={(value) => setData('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map(status => (
                      <SelectItem key={status} value={status}>
                        <div className="flex flex-col">
                          <span className="capitalize">{status}</span>
                          <span className="text-xs text-muted-foreground">
                            {getStatusDescription(status)}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-600">{errors.status}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {getStatusDescription(data.status || '')}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="error_message">Error Message</Label>
                <Textarea
                  id="error_message"
                  placeholder="Enter error message if status is failed..."
                  value={data.error_message}
                  onChange={(e) => setData('error_message', e.target.value)}
                  rows={4}
                />
                {errors.error_message && (
                  <p className="text-sm text-red-600">{errors.error_message}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Provide details about any errors that occurred during processing
                </p>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" asChild>
                  <Link href={route('report-batches.show', reportBatch.id)}>
                    Cancel
                  </Link>
                </Button>
                <Button type="submit" disabled={processing}>
                  {processing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Update Report Batch
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Warning for Completed Reports */}
        {reportBatch.status === 'completed' && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800">Completed Report Batch</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This report batch has been completed. Changing the status may affect data integrity. 
                    Please ensure you understand the implications before making changes.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}

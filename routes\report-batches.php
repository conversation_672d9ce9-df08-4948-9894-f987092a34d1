<?php

use App\Http\Controllers\ReportBatchController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard route
    Route::get('/report-batches/dashboard', [ReportBatchController::class, 'dashboard'])
        ->name('report-batches.dashboard');

    // Resource routes for report batches
    Route::resource('report-batches', ReportBatchController::class);

    // Additional action routes
    Route::prefix('report-batches/{reportBatch}')->group(function () {
        // Data validation and generation
        Route::post('/validate-data', [ReportBatchController::class, 'validateData'])
            ->name('report-batches.validate-data');
        
        Route::post('/generate-data', [ReportBatchController::class, 'generateData'])
            ->name('report-batches.generate-data');

        // Export routes
        Route::get('/export/json', [ReportBatchController::class, 'exportJson'])
            ->name('report-batches.export.json');
        
        Route::get('/export/csv', [ReportBatchController::class, 'exportCsv'])
            ->name('report-batches.export.csv');
    });

    // Bulk operations
    Route::post('/report-batches-bulk/export', [ReportBatchController::class, 'bulkExport'])
        ->name('report-batches.bulk.export');
});

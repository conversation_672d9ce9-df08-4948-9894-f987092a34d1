import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  FileText,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Plus,
  Filter
} from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';

import { DashboardStatistics, MONTH_NAMES } from '@/types/report-batches';

interface Props {
  statistics: DashboardStatistics;
  currentYear: number;
  availableYears: number[];
}

export default function ReportBatchesDashboard({ statistics, currentYear, availableYears }: Props) {
  const [selectedYear, setSelectedYear] = useState(currentYear);

  const handleYearChange = (year: string) => {
    const newYear = parseInt(year);
    setSelectedYear(newYear);
    router.get(route('report-batches.dashboard'), { year: newYear }, {
      preserveState: true,
      replace: true,
    });
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getCompletionBadge = (completed: number, total: number) => {
    if (total === 0) return <Badge variant="secondary">No Data</Badge>;
    
    const percentage = (completed / total) * 100;
    if (percentage === 100) return <Badge variant="default">Complete</Badge>;
    if (percentage >= 70) return <Badge variant="secondary">In Progress</Badge>;
    return <Badge variant="destructive">Behind</Badge>;
  };

  return (
    <AppLayout>
      <Head title="Report Batches Dashboard" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Report Batches Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor report generation status and progress for {selectedYear}
            </p>
          </div>
          <div className="flex gap-2">
            <Select value={selectedYear.toString()} onValueChange={handleYearChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableYears.map(year => (
                  <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button asChild variant="outline">
              <Link href={route('report-batches.index')}>
                <FileText className="h-4 w-4 mr-2" />
                View All Batches
              </Link>
            </Button>
            <Button asChild>
              <Link href={route('report-batches.create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Report Batch
              </Link>
            </Button>
          </div>
        </div>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Batches</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total_batches}</div>
              <p className="text-xs text-muted-foreground">
                Report batches in {selectedYear}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statistics.completed_batches}</div>
              <p className="text-xs text-muted-foreground">
                Successfully completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{statistics.pending_batches}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting processing
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statistics.failed_batches}</div>
              <p className="text-xs text-muted-foreground">
                Processing failed
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Completion Rate */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Overall Completion Rate
            </CardTitle>
            <CardDescription>
              Percentage of completed report batches for {selectedYear}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Completion Progress</span>
                <span className={`text-2xl font-bold ${getCompletionColor(statistics.completion_rate)}`}>
                  {statistics.completion_rate}%
                </span>
              </div>
              <Progress value={statistics.completion_rate} className="w-full h-3" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{statistics.completed_batches} completed</span>
                <span>{statistics.total_batches} total</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Monthly Progress
            </CardTitle>
            <CardDescription>
              Report batch completion status by month for {selectedYear}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {statistics.monthly_progress.map((month) => (
                <Card key={month.month} className="border-2">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-medium">
                        {month.month_name}
                      </CardTitle>
                      {getCompletionBadge(month.completed, month.total)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Progress</span>
                        <span className="font-medium">
                          {month.total > 0 ? Math.round((month.completed / month.total) * 100) : 0}%
                        </span>
                      </div>
                      
                      <Progress 
                        value={month.total > 0 ? (month.completed / month.total) * 100 : 0} 
                        className="w-full h-2" 
                      />
                      
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{month.completed} completed</span>
                        <span>{month.total} total</span>
                      </div>

                      {month.total > 0 && (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full"
                          asChild
                        >
                          <Link href={route('report-batches.index', { 
                            year: selectedYear, 
                            month: month.month 
                          })}>
                            View Details
                          </Link>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button asChild variant="outline" className="h-auto p-4">
                <Link href={route('report-batches.create')}>
                  <div className="text-center">
                    <Plus className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-medium">Create New Batch</div>
                    <div className="text-xs text-muted-foreground">
                      Generate reports for a new period
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4">
                <Link href={route('report-batches.index', { status: 'pending' })}>
                  <div className="text-center">
                    <Clock className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-medium">View Pending</div>
                    <div className="text-xs text-muted-foreground">
                      {statistics.pending_batches} batches awaiting processing
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4">
                <Link href={route('report-batches.index', { status: 'failed' })}>
                  <div className="text-center">
                    <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-medium">Review Failed</div>
                    <div className="text-xs text-muted-foreground">
                      {statistics.failed_batches} batches need attention
                    </div>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity Summary */}
        {statistics.total_batches === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Report Batches Yet</h3>
                <p className="text-muted-foreground mb-4">
                  Get started by creating your first report batch for {selectedYear}.
                </p>
                <Button asChild>
                  <Link href={route('report-batches.create')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Report Batch
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}

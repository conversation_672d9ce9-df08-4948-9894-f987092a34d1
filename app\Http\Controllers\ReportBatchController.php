<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReportBatch\StoreReportBatchRequest;
use App\Http\Requests\ReportBatch\UpdateReportBatchRequest;
use App\Models\ReportBatch;
use App\Services\ReportBatchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class ReportBatchController extends Controller
{
    public function __construct(
        protected ReportBatchService $reportBatchService
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): InertiaResponse
    {
        $filters = $request->only([
            'year', 'month', 'report_code', 'status',
            'institution_code', 'branch_code', 'search'
        ]);

        $perPage = $request->get('per_page', 15);
        $reportBatches = $this->reportBatchService->getPaginatedReportBatches($filters, $perPage);

        // Get available filter options
        $reportCodes = array_keys(config('report-mapping', []));
        $years = range(2020, (int) date('Y'));
        $months = range(1, 12);
        $statuses = [
            ReportBatch::STATUS_PENDING,
            ReportBatch::STATUS_PROCESSING,
            ReportBatch::STATUS_COMPLETED,
            ReportBatch::STATUS_FAILED,
        ];

        return Inertia::render('ReportBatches/Index', [
            'reportBatches' => $reportBatches,
            'filters' => $filters,
            'filterOptions' => [
                'reportCodes' => $reportCodes,
                'years' => $years,
                'months' => $months,
                'statuses' => $statuses,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): InertiaResponse
    {
        $reportCodes = array_keys(config('report-mapping', []));
        $currentPeriod = $this->reportBatchService->getCurrentPeriodSuggestion();

        return Inertia::render('ReportBatches/Create', [
            'reportCodes' => $reportCodes,
            'currentPeriod' => $currentPeriod,
            'institutionCode' => config('app.office_code'),
            'branchCode' => config('app.branch_code'),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreReportBatchRequest $request): JsonResponse
    {
        try {
            $reportBatch = ReportBatch::create($request->validatedWithDefaults());

            return response()->json([
                'success' => true,
                'message' => 'Report batch created successfully.',
                'data' => $reportBatch->load([]),
                'redirect' => route('report-batches.show', $reportBatch),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create report batch.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ReportBatch $reportBatch): InertiaResponse
    {
        // Load related data based on report type
        $reportData = $reportBatch->getReportData();

        // Get validation results
        $validationResults = $this->reportBatchService->validateDataAvailability($reportBatch);

        return Inertia::render('ReportBatches/Show', [
            'reportBatch' => $reportBatch,
            'reportData' => $reportData->paginate(50), // Paginate the related data
            'validationResults' => $validationResults,
            'previousPeriod' => $reportBatch->getPreviousPeriod(),
            'comparisonPeriod' => $reportBatch->getComparisonPeriod(),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReportBatch $reportBatch): InertiaResponse
    {
        return Inertia::render('ReportBatches/Edit', [
            'reportBatch' => $reportBatch,
            'statuses' => [
                ReportBatch::STATUS_PENDING,
                ReportBatch::STATUS_PROCESSING,
                ReportBatch::STATUS_COMPLETED,
                ReportBatch::STATUS_FAILED,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateReportBatchRequest $request, ReportBatch $reportBatch): JsonResponse
    {
        try {
            $reportBatch->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Report batch updated successfully.',
                'data' => $reportBatch->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update report batch.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReportBatch $reportBatch): JsonResponse
    {
        try {
            // Prevent deletion of completed reports
            if ($reportBatch->isCompleted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete completed report batches.',
                ], 422);
            }

            $reportBatch->delete();

            return response()->json([
                'success' => true,
                'message' => 'Report batch deleted successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete report batch.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate data availability for report generation
     */
    public function validateData(ReportBatch $reportBatch): JsonResponse
    {
        try {
            $validationResults = $this->reportBatchService->validateDataAvailability($reportBatch);

            return response()->json([
                'success' => true,
                'validation_results' => $validationResults,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate data.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate report data
     */
    public function generateData(ReportBatch $reportBatch): JsonResponse
    {
        try {
            // Prevent generation if already processing or completed
            if ($reportBatch->isProcessing()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report batch is already being processed.',
                ], 422);
            }

            if ($reportBatch->isCompleted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report batch is already completed.',
                ], 422);
            }

            $result = $this->reportBatchService->generateReportData($reportBatch);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report data.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export report batch data as JSON
     */
    public function exportJson(ReportBatch $reportBatch): JsonResponse
    {
        try {
            $data = $reportBatch->getReportData()->get();

            $filename = sprintf(
                'report_%s_%s_%04d_%02d.json',
                $reportBatch->report_code,
                $reportBatch->institution_code,
                $reportBatch->report_year,
                $reportBatch->report_month
            );

            return response()->json([
                'success' => true,
                'data' => $data,
                'filename' => $filename,
                'metadata' => [
                    'report_batch_id' => $reportBatch->id,
                    'report_code' => $reportBatch->report_code,
                    'period' => $reportBatch->period_name,
                    'record_count' => $data->count(),
                    'exported_at' => now()->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export data.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export report batch data as CSV
     */
    public function exportCsv(ReportBatch $reportBatch): Response
    {
        try {
            $data = $reportBatch->getReportData()->get();

            $filename = sprintf(
                'report_%s_%s_%04d_%02d.csv',
                $reportBatch->report_code,
                $reportBatch->institution_code,
                $reportBatch->report_year,
                $reportBatch->report_month
            );

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($data) {
                $file = fopen('php://output', 'w');

                // Add CSV headers if data exists
                if ($data->isNotEmpty()) {
                    $firstRow = $data->first();
                    if (is_array($firstRow->toArray())) {
                        fputcsv($file, array_keys($firstRow->toArray()));
                    }
                }

                // Add data rows
                foreach ($data as $row) {
                    fputcsv($file, $row->toArray());
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export CSV.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk export multiple report batches
     */
    public function bulkExport(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'report_batch_ids' => 'required|array',
                'report_batch_ids.*' => 'exists:report_batches,id',
                'format' => 'required|in:json,csv',
            ]);

            $reportBatches = ReportBatch::whereIn('id', $request->report_batch_ids)->get();
            $format = $request->format;

            $exportData = [];
            foreach ($reportBatches as $reportBatch) {
                $data = $reportBatch->getReportData()->get();
                $exportData[] = [
                    'report_batch' => $reportBatch,
                    'data' => $data,
                ];
            }

            $filename = sprintf(
                'bulk_export_%s_%s.%s',
                now()->format('Y_m_d_H_i_s'),
                count($reportBatches),
                $format
            );

            return response()->json([
                'success' => true,
                'export_data' => $exportData,
                'filename' => $filename,
                'metadata' => [
                    'total_batches' => count($reportBatches),
                    'total_records' => collect($exportData)->sum(fn($item) => $item['data']->count()),
                    'exported_at' => now()->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk export.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Dashboard view with statistics
     */
    public function dashboard(Request $request): InertiaResponse
    {
        $year = $request->get('year', (int) date('Y'));
        $statistics = $this->reportBatchService->getDashboardStatistics($year);

        return Inertia::render('ReportBatches/Dashboard', [
            'statistics' => $statistics,
            'currentYear' => $year,
            'availableYears' => range(2020, (int) date('Y')),
        ]);
    }
}

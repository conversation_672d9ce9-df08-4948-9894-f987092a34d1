export interface ReportType {
  id: string;
  name: string;
  description: string | null;
  field_definitions: {
    fields: {
      name: string;
      position: number;
      type: string;
    }[];
  } | null;
  created_at: string;
  updated_at: string;
}

export interface Report {
  id: number;
  report_code: string;
  branch_code: string;
  office_code: string;
  year: number;
  month: string;
  report_type_id: string;
  version: string;
  record_count: number;
  processed: boolean;
  file_path: string | null;
  created_at: string;
  updated_at: string;
  report_type?: ReportType;
}
